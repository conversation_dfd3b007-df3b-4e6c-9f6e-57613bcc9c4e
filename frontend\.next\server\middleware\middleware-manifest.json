{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "e71193cd32ba30f4f72b5d9e45ca9869", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "955e8d45674c54102222929fbc3c1c6e202d1047b575ce87cbbf2550ba9803e6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8dc1576cc68a5a41f897239e00cebbe51fae1b9467da8a69abc4a0f8fa8818a5"}}}, "instrumentation": null, "functions": {}}