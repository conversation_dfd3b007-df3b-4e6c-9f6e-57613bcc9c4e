{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;;IAC7B,MAAM,uBAAuB,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;GARa;;QACkB,0IAAA,CAAA,kBAAe;;;AAUvC,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;qCACrC,CAAC,WAAa;;qCACd,CAAC;YACC,qCAAqC;YACrC,qBAAqB,OAAO,eAAe;YAC3C,OAAO,QAAQ,MAAM,CAAC;QACxB;;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAIW;AAJX;AACA;;;AAEA,MAAM,SAAS,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,wCAAmC;gBACjC,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,yBAAyB;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/applications.js"], "sourcesContent": ["import client from './client';\r\n\r\n/**\r\n * Applications API functions for admin dashboard\r\n */\r\n\r\n// Get all applications with advanced filtering and pagination\r\nexport function getAllApplications(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Pagination\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.page_size) queryParams.append('page_size', params.page_size);\r\n  \r\n  // Filtering\r\n  if (params.status && params.status !== 'ALL') queryParams.append('status', params.status);\r\n  if (params.job_id) queryParams.append('job_id', params.job_id); // Add job_id filter\r\n  if (params.company) queryParams.append('company_name__icontains', params.company);\r\n  if (params.job_title) queryParams.append('job__title__icontains', params.job_title);\r\n  if (params.student_name) queryParams.append('student_name__icontains', params.student_name);\r\n  if (params.date_from) queryParams.append('applied_at__gte', params.date_from);\r\n  if (params.date_to) queryParams.append('applied_at__lte', params.date_to);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/jobs/applications/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Get individual application details\r\nexport function getApplication(applicationId) {\r\n  return client.get(`/api/v1/jobs/applications/${applicationId}/`);\r\n}\r\n\r\n// Update application (admin only)\r\nexport function updateApplication(applicationId, data) {\r\n  return client.patch(`/api/v1/jobs/applications/${applicationId}/`, data);\r\n}\r\n\r\n// Delete application (soft delete)\r\nexport function deleteApplication(applicationId) {\r\n  return client.delete(`/api/v1/jobs/applications/${applicationId}/`);\r\n}\r\n\r\n// Get application statistics\r\nexport function getApplicationStats(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  if (params.job_id) queryParams.append('job_id', params.job_id);\r\n  if (params.date_from) queryParams.append('date_from', params.date_from);\r\n  if (params.date_to) queryParams.append('date_to', params.date_to);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/jobs/applications/stats/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Export applications\r\nexport function exportApplications(config) {\r\n  return client.post('/api/v1/jobs/applications/export/', config, {\r\n    responseType: 'blob' // Important for file downloads\r\n  });\r\n}\r\n\r\n// Bulk update applications\r\nexport function bulkUpdateApplications(applicationIds, updateData) {\r\n  return client.post('/api/v1/jobs/applications/bulk-update/', {\r\n    application_ids: applicationIds,\r\n    update_data: updateData\r\n  });\r\n}\r\n\r\n// Get available profile fields for form configuration\r\nexport function getProfileFields() {\r\n  return client.get('/api/v1/jobs/applications/fields/');\r\n}\r\n\r\n// Get applications for a specific job (used in job detail page)\r\nexport function getJobApplications(jobId, params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.page_size) queryParams.append('page_size', params.page_size);\r\n  if (params.status) queryParams.append('status', params.status);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/${jobId}/applications/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Application status change tracking\r\nexport function updateApplicationStatus(applicationId, newStatus, notes = '') {\r\n  return client.patch(`/api/v1/jobs/applications/${applicationId}/status/`, {\r\n    status: newStatus,\r\n    admin_notes: notes\r\n  });\r\n}\r\n\r\n// Get status history for an application\r\nexport function getApplicationStatusHistory(applicationId) {\r\n  return client.get(`/api/v1/jobs/applications/${applicationId}/status-history/`);\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAOO,SAAS,mBAAmB,SAAS,CAAC,CAAC;IAC5C,MAAM,cAAc,IAAI;IAExB,aAAa;IACb,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;IAEtE,YAAY;IACZ,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IACxF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,GAAG,oBAAoB;IACpF,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,2BAA2B,OAAO,OAAO;IAChF,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,yBAAyB,OAAO,SAAS;IAClF,IAAI,OAAO,YAAY,EAAE,YAAY,MAAM,CAAC,2BAA2B,OAAO,YAAY;IAC1F,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,mBAAmB,OAAO,SAAS;IAC5E,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,mBAAmB,OAAO,OAAO;IAExE,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE/E,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,eAAe,aAAa;IAC1C,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC;AACjE;AAGO,SAAS,kBAAkB,aAAa,EAAE,IAAI;IACnD,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC,EAAE;AACrE;AAGO,SAAS,kBAAkB,aAAa;IAC7C,OAAO,uHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC;AACpE;AAGO,SAAS,oBAAoB,SAAS,CAAC,CAAC;IAC7C,MAAM,cAAc,IAAI;IACxB,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;IACtE,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;IAEhE,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,gCAAgC,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAErF,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,mBAAmB,MAAM;IACvC,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,qCAAqC,QAAQ;QAC9D,cAAc,OAAO,+BAA+B;IACtD;AACF;AAGO,SAAS,uBAAuB,cAAc,EAAE,UAAU;IAC/D,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,0CAA0C;QAC3D,iBAAiB;QACjB,aAAa;IACf;AACF;AAGO,SAAS;IACd,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,mBAAmB,KAAK,EAAE,SAAS,CAAC,CAAC;IACnD,MAAM,cAAc,IAAI;IACxB,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;IACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAE7D,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,qCAAqC,EAAE,MAAM,cAAc,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAEhH,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,wBAAwB,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE;IAC1E,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,cAAc,QAAQ,CAAC,EAAE;QACxE,QAAQ;QACR,aAAa;IACf;AACF;AAGO,SAAS,4BAA4B,aAAa;IACvD,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,cAAc,gBAAgB,CAAC;AAChF", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/applications/components/ApplicationFilters.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { X, Calendar } from 'lucide-react';\r\n\r\nexport default function ApplicationFilters({ filters, onFilterChange, onClose }) {\r\n  const [localFilters, setLocalFilters] = useState(filters);\r\n\r\n  const statusOptions = [\r\n    { value: 'ALL', label: 'All Statuses' },\r\n    { value: 'APPLIED', label: 'Applied' },\r\n    { value: 'UNDER_REVIEW', label: 'Under Review' },\r\n    { value: 'SHORTLISTED', label: 'Shortlisted' },\r\n    { value: 'REJECTED', label: 'Rejected' },\r\n    { value: 'HIRED', label: 'Hired' }\r\n  ];\r\n\r\n  const handleFilterChange = (key, value) => {\r\n    const newFilters = { ...localFilters, [key]: value };\r\n    setLocalFilters(newFilters);\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    onFilterChange(localFilters);\r\n    onClose();\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    const clearedFilters = {\r\n      status: 'ALL',\r\n      company: '',\r\n      job_title: '',\r\n      date_from: '',\r\n      date_to: '',\r\n      student_name: ''\r\n    };\r\n    setLocalFilters(clearedFilters);\r\n    onFilterChange(clearedFilters);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900\">Filter Applications</h3>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n        >\r\n          <X className=\"w-5 h-5\" />\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\r\n        {/* Status Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Status\r\n          </label>\r\n          <select\r\n            value={localFilters.status}\r\n            onChange={(e) => handleFilterChange('status', e.target.value)}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          >\r\n            {statusOptions.map(option => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Company Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Company\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Enter company name\"\r\n            value={localFilters.company}\r\n            onChange={(e) => handleFilterChange('company', e.target.value)}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        {/* Job Title Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Job Title\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Enter job title\"\r\n            value={localFilters.job_title}\r\n            onChange={(e) => handleFilterChange('job_title', e.target.value)}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        {/* Student Name Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Student Name\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Enter student name\"\r\n            value={localFilters.student_name}\r\n            onChange={(e) => handleFilterChange('student_name', e.target.value)}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        {/* Date From Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Applied From\r\n          </label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"date\"\r\n              value={localFilters.date_from}\r\n              onChange={(e) => handleFilterChange('date_from', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Date To Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Applied To\r\n          </label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"date\"\r\n              value={localFilters.date_to}\r\n              onChange={(e) => handleFilterChange('date_to', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <button\r\n          onClick={clearFilters}\r\n          className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n        >\r\n          Clear All\r\n        </button>\r\n        \r\n        <div className=\"flex gap-3\">\r\n          <button\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            onClick={applyFilters}\r\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\r\n          >\r\n            Apply Filters\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKe,SAAS,mBAAmB,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE;;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAe;QACtC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAgB,OAAO;QAAe;QAC/C;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,MAAM,qBAAqB,CAAC,KAAK;QAC/B,MAAM,aAAa;YAAE,GAAG,YAAY;YAAE,CAAC,IAAI,EAAE;QAAM;QACnD,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,eAAe;QACf;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,iBAAiB;YACrB,QAAQ;YACR,SAAS;YACT,WAAW;YACX,WAAW;YACX,SAAS;YACT,cAAc;QAChB;QACA,gBAAgB;QAChB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,OAAO,aAAa,MAAM;gCAC1B,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;0CAET,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,aAAa,OAAO;gCAC3B,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAU;;;;;;;;;;;;kCAKd,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,aAAa,SAAS;gCAC7B,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAU;;;;;;;;;;;;kCAKd,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,aAAa,YAAY;gCAChC,UAAU,CAAC,IAAM,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAClE,WAAU;;;;;;;;;;;;kCAKd,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO,aAAa,SAAS;wCAC7B,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC/D,WAAU;;;;;;kDAEZ,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAKxB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO,aAAa,OAAO;wCAC3B,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;;;;;;kDAEZ,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAvKwB;KAAA", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/components/applications/StatusBadge.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Check<PERSON><PERSON>cle, Clock, Eye, XCircle, Award } from 'lucide-react';\r\n\r\nexport default function StatusBadge({ status }) {\r\n  const getStatusConfig = () => {\r\n    switch (status) {\r\n      case 'APPLIED':\r\n        return {\r\n          color: 'bg-blue-100 text-blue-800 border-blue-200',\r\n          icon: <Clock className=\"w-3 h-3\" />,\r\n          label: 'Applied'\r\n        };\r\n      case 'UNDER_REVIEW':\r\n        return {\r\n          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',\r\n          icon: <Eye className=\"w-3 h-3\" />,\r\n          label: 'Under Review'\r\n        };\r\n      case 'SHORTLISTED':\r\n        return {\r\n          color: 'bg-green-100 text-green-800 border-green-200',\r\n          icon: <CheckCircle className=\"w-3 h-3\" />,\r\n          label: 'Shortlisted'\r\n        };\r\n      case 'REJECTED':\r\n        return {\r\n          color: 'bg-red-100 text-red-800 border-red-200',\r\n          icon: <XCircle className=\"w-3 h-3\" />,\r\n          label: 'Rejected'\r\n        };\r\n      case 'HIRED':\r\n        return {\r\n          color: 'bg-emerald-100 text-emerald-800 border-emerald-200',\r\n          icon: <Award className=\"w-3 h-3\" />,\r\n          label: 'Hired'\r\n        };\r\n      default:\r\n        return {\r\n          color: 'bg-gray-100 text-gray-800 border-gray-200',\r\n          icon: <Clock className=\"w-3 h-3\" />,\r\n          label: status || 'Unknown'\r\n        };\r\n    }\r\n  };\r\n\r\n  const config = getStatusConfig();\r\n\r\n  return (\r\n    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config.color}`}>\r\n      {config.icon}\r\n      {config.label}\r\n    </span>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS,YAAY,EAAE,MAAM,EAAE;IAC5C,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;oBACrB,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,+MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAO;gBACT;YACF;gBACE,OAAO;oBACL,OAAO;oBACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAO,UAAU;gBACnB;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,6LAAC;QAAK,WAAW,CAAC,iFAAiF,EAAE,OAAO,KAAK,EAAE;;YAChH,OAAO,IAAI;YACX,OAAO,KAAK;;;;;;;AAGnB;KAlDwB", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/applications/components/ApplicationsTable.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Eye, Edit, Trash2, MoreHorizontal } from 'lucide-react';\r\nimport { useState } from 'react';\r\nimport StatusBadge from '../../../../components/applications/StatusBadge';\r\n\r\nexport default function ApplicationsTable({ \r\n  applications, \r\n  onViewApplication, \r\n  onDeleteApplication \r\n}) {\r\n  const [selectedRows, setSelectedRows] = useState(new Set());\r\n  const [showBulkActions, setShowBulkActions] = useState(false);\r\n\r\n  // Ensure applications is always an array\r\n  const safeApplications = Array.isArray(applications) ? applications : [];\r\n\r\n  const toggleRowSelection = (applicationId) => {\r\n    const newSelection = new Set(selectedRows);\r\n    if (newSelection.has(applicationId)) {\r\n      newSelection.delete(applicationId);\r\n    } else {\r\n      newSelection.add(applicationId);\r\n    }\r\n    setSelectedRows(newSelection);\r\n    setShowBulkActions(newSelection.size > 0);\r\n  };\r\n\r\n  const toggleAllSelection = () => {\r\n    if (selectedRows.size === safeApplications.length) {\r\n      setSelectedRows(new Set());\r\n      setShowBulkActions(false);\r\n    } else {\r\n      setSelectedRows(new Set(safeApplications.map(app => app.id)));\r\n      setShowBulkActions(true);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"overflow-x-auto\">\r\n      {/* Bulk Actions Bar */}\r\n      {showBulkActions && (\r\n        <div className=\"flex items-center justify-between bg-blue-50 border-b border-blue-200 px-6 py-3\">\r\n          <span className=\"text-sm text-blue-700\">\r\n            {selectedRows.size} application{selectedRows.size !== 1 ? 's' : ''} selected\r\n          </span>\r\n          <div className=\"flex gap-2\">\r\n            <button className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\">\r\n              Bulk Status Update\r\n            </button>\r\n            <button className=\"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700\">\r\n              Bulk Delete\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <table className=\"min-w-full divide-y divide-gray-200\">\r\n        <thead className=\"bg-gray-50\">\r\n          <tr>\r\n            <th className=\"px-6 py-3 text-left\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedRows.size === safeApplications.length && safeApplications.length > 0}\r\n                onChange={toggleAllSelection}\r\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n              />\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Student\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Job Title\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Company\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Status\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Applied Date\r\n            </th>\r\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Actions\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n          {safeApplications.map((application) => (\r\n            <tr \r\n              key={application.id}\r\n              className={`hover:bg-gray-50 ${\r\n                selectedRows.has(application.id) ? 'bg-blue-50' : ''\r\n              }`}\r\n            >\r\n              <td className=\"px-6 py-4\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={selectedRows.has(application.id)}\r\n                  onChange={() => toggleRowSelection(application.id)}\r\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                />\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0 h-10 w-10\">\r\n                    <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        {application.student_name?.charAt(0) || 'U'}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <div className=\"text-sm font-medium text-gray-900\">\r\n                      {application.student_name}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      {application.student_email}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-400\">\r\n                      ID: {application.student_id}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4\">\r\n                <div className=\"text-sm font-medium text-gray-900\">\r\n                  {application.job_title}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">\r\n                  {application.job_location}\r\n                </div>\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                <div className=\"text-sm font-medium text-gray-900\">\r\n                  {application.company_name}\r\n                </div>\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge status={application.status} />\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                {formatDate(application.applied_at)}\r\n              </td>\r\n              \r\n              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <button\r\n                    onClick={() => onViewApplication(application)}\r\n                    className=\"inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  >\r\n                    <Eye className=\"w-3 h-3 mr-1\" />\r\n                    View\r\n                  </button>\r\n                  \r\n                  <button\r\n                    onClick={() => onDeleteApplication(application.id)}\r\n                    className=\"inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n                  >\r\n                    <Trash2 className=\"w-3 h-3 mr-1\" />\r\n                    Delete\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,kBAAkB,EACxC,YAAY,EACZ,iBAAiB,EACjB,mBAAmB,EACpB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,yCAAyC;IACzC,MAAM,mBAAmB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;IAExE,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,IAAI,IAAI;QAC7B,IAAI,aAAa,GAAG,CAAC,gBAAgB;YACnC,aAAa,MAAM,CAAC;QACtB,OAAO;YACL,aAAa,GAAG,CAAC;QACnB;QACA,gBAAgB;QAChB,mBAAmB,aAAa,IAAI,GAAG;IACzC;IAEA,MAAM,qBAAqB;QACzB,IAAI,aAAa,IAAI,KAAK,iBAAiB,MAAM,EAAE;YACjD,gBAAgB,IAAI;YACpB,mBAAmB;QACrB,OAAO;YACL,gBAAgB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;YAC1D,mBAAmB;QACrB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;4BACb,aAAa,IAAI;4BAAC;4BAAa,aAAa,IAAI,KAAK,IAAI,MAAM;4BAAG;;;;;;;kCAErE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAqE;;;;;;0CAGvF,6LAAC;gCAAO,WAAU;0CAAmE;;;;;;;;;;;;;;;;;;0BAO3F,6LAAC;gBAAM,WAAU;;kCACf,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS,aAAa,IAAI,KAAK,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,GAAG;wCACpF,UAAU;wCACV,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;;;;;;;;;;;;kCAKnG,6LAAC;wBAAM,WAAU;kCACd,iBAAiB,GAAG,CAAC,CAAC,4BACrB,6LAAC;gCAEC,WAAW,CAAC,iBAAiB,EAC3B,aAAa,GAAG,CAAC,YAAY,EAAE,IAAI,eAAe,IAClD;;kDAEF,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,aAAa,GAAG,CAAC,YAAY,EAAE;4CACxC,UAAU,IAAM,mBAAmB,YAAY,EAAE;4CACjD,WAAU;;;;;;;;;;;kDAId,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,YAAY,YAAY,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;8DAI9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,YAAY,YAAY;;;;;;sEAE3B,6LAAC;4DAAI,WAAU;sEACZ,YAAY,aAAa;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;;gEAAwB;gEAChC,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMnC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;0DACZ,YAAY,SAAS;;;;;;0DAExB,6LAAC;gDAAI,WAAU;0DACZ,YAAY,YAAY;;;;;;;;;;;;kDAI7B,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;sDACZ,YAAY,YAAY;;;;;;;;;;;kDAI7B,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC,oJAAA,CAAA,UAAW;4CAAC,QAAQ,YAAY,MAAM;;;;;;;;;;;kDAGzC,6LAAC;wCAAG,WAAU;kDACX,WAAW,YAAY,UAAU;;;;;;kDAGpC,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;sEAEV,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIlC,6LAAC;oDACC,SAAS,IAAM,oBAAoB,YAAY,EAAE;oDACjD,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BA1EpC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AAqFjC;GApLwB;KAAA", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/applications/components/ExportModal.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { X, Download, RefreshCw } from 'lucide-react';\r\nimport { useNotification } from '../../../../contexts/NotificationContext';\r\nimport client from '../../../../api/client';\r\n\r\nexport default function ExportModal({ onClose, filters }) {\r\n  const { showSuccess, showError, handleApiError } = useNotification();\r\n  const [exportFormat, setExportFormat] = useState('csv');\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [loadingColumns, setLoadingColumns] = useState(true);\r\n  const [availableColumns, setAvailableColumns] = useState([]);\r\n  const [exportColumns, setExportColumns] = useState([\r\n    'student_name',\r\n    'student_email',\r\n    'job_title',\r\n    'company_name',\r\n    'status',\r\n    'applied_at'\r\n  ]);\r\n\r\n  // Fetch available columns including dynamic fields\r\n  useEffect(() => {\r\n    const fetchAvailableColumns = async () => {\r\n      try {\r\n        setLoadingColumns(true);\r\n        const jobId = filters?.job_id;\r\n        const response = await client.get(`/api/v1/jobs/applications/export/${jobId ? `?job_id=${jobId}` : ''}`);\r\n        \r\n        if (response.data && response.data.all) {\r\n          setAvailableColumns(response.data.all);\r\n          \r\n          // Set default columns if none selected\r\n          if (exportColumns.length === 6) { // Only if still using defaults\r\n            const defaultColumns = response.data.standard\r\n              .filter(col => ['student_name', 'student_email', 'job_title', 'company_name', 'status', 'applied_at'].includes(col.key))\r\n              .map(col => col.key);\r\n            setExportColumns(defaultColumns);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to fetch available columns:', error);\r\n        // Fallback to basic columns\r\n        setAvailableColumns([\r\n          { key: 'student_name', label: 'Student Name', category: 'student' },\r\n          { key: 'student_email', label: 'Email', category: 'student' },\r\n          { key: 'student_id', label: 'Student ID', category: 'student' },\r\n          { key: 'job_title', label: 'Job Title', category: 'job' },\r\n          { key: 'company_name', label: 'Company', category: 'job' },\r\n          { key: 'status', label: 'Status', category: 'application' },\r\n          { key: 'applied_at', label: 'Applied Date', category: 'application' },\r\n          { key: 'current_cgpa', label: 'CGPA', category: 'academic' },\r\n          { key: 'branch', label: 'Branch', category: 'student' }\r\n        ]);\r\n      } finally {\r\n        setLoadingColumns(false);\r\n      }\r\n    };\r\n\r\n    fetchAvailableColumns();\r\n  }, [filters?.job_id]);\r\n\r\n  const handleExport = async () => {\r\n    if (exportColumns.length === 0) {\r\n      showError('Export Error', 'Please select at least one column to export.');\r\n      return;\r\n    }\r\n\r\n    setIsExporting(true);\r\n    \r\n    try {\r\n      // Prepare export request payload with proper filter transformation\r\n      const exportData = {\r\n        format: exportFormat,\r\n        columns: exportColumns,\r\n      };\r\n\r\n      // Transform filters to match backend expectations\r\n      if (filters) {\r\n        // Only include valid filters and transform them appropriately\r\n        if (filters.job_id) {\r\n          exportData.job_id = parseInt(filters.job_id);\r\n        }\r\n        \r\n        if (filters.status && filters.status !== 'ALL') {\r\n          exportData.status = [filters.status]; // Backend expects array\r\n        }\r\n        \r\n        if (filters.date_from) {\r\n          exportData.date_from = filters.date_from;\r\n        }\r\n        \r\n        if (filters.date_to) {\r\n          exportData.date_to = filters.date_to;\r\n        }\r\n        \r\n        // Note: Other filters like company, job_title, student_name are handled differently in the backend\r\n        // They are applied at the queryset level, not in the export configuration\r\n      }\r\n\r\n      console.log('Export request payload:', exportData);\r\n\r\n      // Make API request to export endpoint using the client\r\n      const response = await client.post('/api/v1/jobs/applications/export/', exportData, {\r\n        responseType: 'blob'\r\n      });\r\n\r\n      // Handle file download\r\n      const blob = response.data;\r\n      const contentDisposition = response.headers['content-disposition'];\r\n      const filename = contentDisposition\r\n        ? contentDisposition.split('filename=')[1]?.replace(/\"/g, '')\r\n        : `applications_export.${exportFormat}`;\r\n\r\n      // Show success message with application count for CSV files\r\n      if (exportFormat === 'csv') {\r\n        try {\r\n          const text = await blob.text();\r\n          const lines = text.split('\\n');\r\n          const applicationCount = lines.length - 1; // Subtract header line\r\n\r\n          console.log(`Exported ${applicationCount} applications to CSV`);\r\n\r\n          showSuccess(\r\n            'Export Successful',\r\n            `Your ${exportFormat.toUpperCase()} file has been downloaded with ${applicationCount} applications.`\r\n          );\r\n        } catch (previewError) {\r\n          console.error('Error reading CSV content:', previewError);\r\n          showSuccess('Export Successful', `Your ${exportFormat.toUpperCase()} file has been downloaded.`);\r\n        }\r\n      } else {\r\n        showSuccess('Export Successful', `Your ${exportFormat.toUpperCase()} file has been downloaded.`);\r\n      }\r\n\r\n      // Create download link\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = filename;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n      onClose();\r\n\r\n    } catch (error) {\r\n      console.error('Export error:', error);\r\n\r\n      // Provide specific error messages\r\n      if (error.response?.status === 401) {\r\n        showError('Authentication Error', 'You need to be logged in as an admin to export data. Please log in and try again.');\r\n      } else if (error.response?.status === 403) {\r\n        showError('Permission Error', 'You do not have permission to export applications data.');\r\n      } else if (error.response?.status === 500) {\r\n        showError('Server Error', 'There was a server error while generating the export. Please try again later.');\r\n      } else {\r\n        handleApiError(error, 'export');\r\n      }\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto\">\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900\">Export Applications</h2>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n          >\r\n            <X className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-6\">\r\n          {/* Information Notice */}\r\n          <div className=\"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\r\n            <div className=\"flex items-start\">\r\n              <div className=\"flex-shrink-0\">\r\n                <svg className=\"w-5 h-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <div className=\"ml-3\">\r\n                <h3 className=\"text-sm font-medium text-blue-800\">Export Information</h3>\r\n                <p className=\"mt-1 text-sm text-blue-700\">\r\n                  This will export <strong>ALL applications</strong> that match your current filters, not just the ones visible on this page.\r\n                  The export contains real-time data from the database.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Export Format */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\r\n              Export Format\r\n            </label>\r\n            <div className=\"flex gap-3\">\r\n              {['csv', 'excel', 'pdf'].map(format => (\r\n                <label key={format} className=\"flex items-center\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"format\"\r\n                    value={format}\r\n                    checked={exportFormat === format}\r\n                    onChange={(e) => setExportFormat(e.target.value)}\r\n                    className=\"mr-2\"\r\n                  />\r\n                  {format.toUpperCase()}\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Column Selection */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\r\n              Select Columns to Export\r\n            </label>\r\n            <p className=\"text-sm text-gray-600 mb-4\">Choose which fields to include in your export file.</p>\r\n            \r\n            {loadingColumns ? (\r\n              <div className=\"flex items-center justify-center py-8 border rounded-lg\">\r\n                <RefreshCw className=\"w-5 h-5 text-gray-400 animate-spin mr-2\" />\r\n                <span className=\"text-gray-600\">Loading available columns...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <div className=\"space-y-4 max-h-64 overflow-y-auto border rounded-lg p-3\">\r\n                  {/* Group columns by category */}\r\n                  {['student', 'job', 'application', 'academic', 'contact', 'additional'].map(category => {\r\n                    const categoryColumns = availableColumns.filter(col => col.category === category);\r\n                    if (categoryColumns.length === 0) return null;\r\n                    \r\n                    const categoryLabels = {\r\n                      student: 'Student Information',\r\n                      job: 'Job Details',\r\n                      application: 'Application Data',\r\n                      academic: 'Academic Information',\r\n                      contact: 'Contact Information',\r\n                      additional: 'Additional Fields'\r\n                    };\r\n                    \r\n                    return (\r\n                      <div key={category} className=\"mb-4\">\r\n                        <h4 className=\"text-sm font-medium text-gray-800 mb-2 border-b border-gray-200 pb-1\">\r\n                          {categoryLabels[category]}\r\n                        </h4>\r\n                        <div className=\"space-y-1\">\r\n                          {categoryColumns.map((column) => (\r\n                            <label key={column.key} className=\"flex items-center space-x-3 p-1 hover:bg-gray-50 rounded cursor-pointer\">\r\n                              <input\r\n                                type=\"checkbox\"\r\n                                checked={exportColumns.includes(column.key)}\r\n                                onChange={(e) => {\r\n                                  if (e.target.checked) {\r\n                                    setExportColumns(prev => [...prev, column.key]);\r\n                                  } else {\r\n                                    setExportColumns(prev => prev.filter(col => col !== column.key));\r\n                                  }\r\n                                }}\r\n                                className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n                              />\r\n                              <span className=\"text-sm text-gray-700 flex-1\">{column.label}</span>\r\n                              {column.type && (\r\n                                <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\r\n                                  {column.type}\r\n                                </span>\r\n                              )}\r\n                            </label>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n\r\n                <div className=\"mt-3 flex justify-between text-sm text-gray-600\">\r\n                  <span>{exportColumns.length} columns selected</span>\r\n                  <div className=\"space-x-2\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setExportColumns([])}\r\n                      className=\"text-gray-600 hover:text-gray-700\"\r\n                    >\r\n                      Clear All\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setExportColumns(availableColumns.map(col => col.key))}\r\n                      className=\"text-blue-600 hover:text-blue-700\"\r\n                    >\r\n                      Select All\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex justify-end gap-3\">\r\n            <button\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              onClick={handleExport}\r\n              disabled={exportColumns.length === 0 || isExporting}\r\n              className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\r\n            >\r\n              <Download className=\"w-4 h-4\" />\r\n              {isExporting ? 'Exporting...' : 'Export'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAOe,SAAS,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;;IACtD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;+DAAwB;oBAC5B,IAAI;wBACF,kBAAkB;wBAClB,MAAM,QAAQ,SAAS;wBACvB,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,QAAQ,CAAC,QAAQ,EAAE,OAAO,GAAG,IAAI;wBAEvG,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE;4BACtC,oBAAoB,SAAS,IAAI,CAAC,GAAG;4BAErC,uCAAuC;4BACvC,IAAI,cAAc,MAAM,KAAK,GAAG;gCAC9B,MAAM,iBAAiB,SAAS,IAAI,CAAC,QAAQ,CAC1C,MAAM;kGAAC,CAAA,MAAO;4CAAC;4CAAgB;4CAAiB;4CAAa;4CAAgB;4CAAU;yCAAa,CAAC,QAAQ,CAAC,IAAI,GAAG;iGACrH,GAAG;kGAAC,CAAA,MAAO,IAAI,GAAG;;gCACrB,iBAAiB;4BACnB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sCAAsC;wBACpD,4BAA4B;wBAC5B,oBAAoB;4BAClB;gCAAE,KAAK;gCAAgB,OAAO;gCAAgB,UAAU;4BAAU;4BAClE;gCAAE,KAAK;gCAAiB,OAAO;gCAAS,UAAU;4BAAU;4BAC5D;gCAAE,KAAK;gCAAc,OAAO;gCAAc,UAAU;4BAAU;4BAC9D;gCAAE,KAAK;gCAAa,OAAO;gCAAa,UAAU;4BAAM;4BACxD;gCAAE,KAAK;gCAAgB,OAAO;gCAAW,UAAU;4BAAM;4BACzD;gCAAE,KAAK;gCAAU,OAAO;gCAAU,UAAU;4BAAc;4BAC1D;gCAAE,KAAK;gCAAc,OAAO;gCAAgB,UAAU;4BAAc;4BACpE;gCAAE,KAAK;gCAAgB,OAAO;gCAAQ,UAAU;4BAAW;4BAC3D;gCAAE,KAAK;gCAAU,OAAO;gCAAU,UAAU;4BAAU;yBACvD;oBACH,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA;QACF;gCAAG;QAAC,SAAS;KAAO;IAEpB,MAAM,eAAe;QACnB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,UAAU,gBAAgB;YAC1B;QACF;QAEA,eAAe;QAEf,IAAI;YACF,mEAAmE;YACnE,MAAM,aAAa;gBACjB,QAAQ;gBACR,SAAS;YACX;YAEA,kDAAkD;YAClD,IAAI,SAAS;gBACX,8DAA8D;gBAC9D,IAAI,QAAQ,MAAM,EAAE;oBAClB,WAAW,MAAM,GAAG,SAAS,QAAQ,MAAM;gBAC7C;gBAEA,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,OAAO;oBAC9C,WAAW,MAAM,GAAG;wBAAC,QAAQ,MAAM;qBAAC,EAAE,wBAAwB;gBAChE;gBAEA,IAAI,QAAQ,SAAS,EAAE;oBACrB,WAAW,SAAS,GAAG,QAAQ,SAAS;gBAC1C;gBAEA,IAAI,QAAQ,OAAO,EAAE;oBACnB,WAAW,OAAO,GAAG,QAAQ,OAAO;gBACtC;YAEA,mGAAmG;YACnG,0EAA0E;YAC5E;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,uDAAuD;YACvD,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,qCAAqC,YAAY;gBAClF,cAAc;YAChB;YAEA,uBAAuB;YACvB,MAAM,OAAO,SAAS,IAAI;YAC1B,MAAM,qBAAqB,SAAS,OAAO,CAAC,sBAAsB;YAClE,MAAM,WAAW,qBACb,mBAAmB,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,QAAQ,MAAM,MACxD,CAAC,oBAAoB,EAAE,cAAc;YAEzC,4DAA4D;YAC5D,IAAI,iBAAiB,OAAO;gBAC1B,IAAI;oBACF,MAAM,OAAO,MAAM,KAAK,IAAI;oBAC5B,MAAM,QAAQ,KAAK,KAAK,CAAC;oBACzB,MAAM,mBAAmB,MAAM,MAAM,GAAG,GAAG,uBAAuB;oBAElE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,iBAAiB,oBAAoB,CAAC;oBAE9D,YACE,qBACA,CAAC,KAAK,EAAE,aAAa,WAAW,GAAG,+BAA+B,EAAE,iBAAiB,cAAc,CAAC;gBAExG,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,YAAY,qBAAqB,CAAC,KAAK,EAAE,aAAa,WAAW,GAAG,0BAA0B,CAAC;gBACjG;YACF,OAAO;gBACL,YAAY,qBAAqB,CAAC,KAAK,EAAE,aAAa,WAAW,GAAG,0BAA0B,CAAC;YACjG;YAEA,uBAAuB;YACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG;YAChB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAE/B,kCAAkC;YAClC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,UAAU,wBAAwB;YACpC,OAAO,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACzC,UAAU,oBAAoB;YAChC,OAAO,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACzC,UAAU,gBAAgB;YAC5B,OAAO;gBACL,eAAe,OAAO;YACxB;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAmI,UAAS;;;;;;;;;;;;;;;;kDAG3K,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;;oDAA6B;kEACvB,6LAAC;kEAAO;;;;;;oDAAyB;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAO;wCAAS;qCAAM,CAAC,GAAG,CAAC,CAAA,uBAC3B,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO;oDACP,SAAS,iBAAiB;oDAC1B,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;;;;;;gDAEX,OAAO,WAAW;;2CATT;;;;;;;;;;;;;;;;sCAgBlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;gCAEzC,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;yDAGlC;;sDACE,6LAAC;4CAAI,WAAU;sDAEZ;gDAAC;gDAAW;gDAAO;gDAAe;gDAAY;gDAAW;6CAAa,CAAC,GAAG,CAAC,CAAA;gDAC1E,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;gDACxE,IAAI,gBAAgB,MAAM,KAAK,GAAG,OAAO;gDAEzC,MAAM,iBAAiB;oDACrB,SAAS;oDACT,KAAK;oDACL,aAAa;oDACb,UAAU;oDACV,SAAS;oDACT,YAAY;gDACd;gDAEA,qBACE,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAG,WAAU;sEACX,cAAc,CAAC,SAAS;;;;;;sEAE3B,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;oEAAuB,WAAU;;sFAChC,6LAAC;4EACC,MAAK;4EACL,SAAS,cAAc,QAAQ,CAAC,OAAO,GAAG;4EAC1C,UAAU,CAAC;gFACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;oFACpB,iBAAiB,CAAA,OAAQ;+FAAI;4FAAM,OAAO,GAAG;yFAAC;gFAChD,OAAO;oFACL,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ,OAAO,GAAG;gFAChE;4EACF;4EACA,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;sFAAgC,OAAO,KAAK;;;;;;wEAC3D,OAAO,IAAI,kBACV,6LAAC;4EAAK,WAAU;sFACb,OAAO,IAAI;;;;;;;mEAhBN,OAAO,GAAG;;;;;;;;;;;mDANlB;;;;;4CA8Bd;;;;;;sDAGF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAM,cAAc,MAAM;wDAAC;;;;;;;8DAC5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB,EAAE;4DAClC,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG;4DACnE,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU,cAAc,MAAM,KAAK,KAAK;oCACxC,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;GA/TwB;;QAC6B,0IAAA,CAAA,kBAAe;;;KAD5C", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/applications/components/ApplicationDetailModal.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { X, Save, User, Briefcase, Calendar, FileText, FileCheck, Download } from 'lucide-react';\r\nimport StatusBadge from '../../../../components/applications/StatusBadge';\r\nimport { getApplication } from '../../../../api/applications';\r\n\r\nexport default function ApplicationDetailModal({ application, onClose, onUpdate }) {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [editData, setEditData] = useState({\r\n    status: application?.status || 'APPLIED',\r\n    admin_notes: application?.admin_notes || ''\r\n  });\r\n  const [detailedApplication, setDetailedApplication] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Fetch detailed application data including job's additional fields\r\n  useEffect(() => {\r\n    const fetchDetailedApplication = async () => {\r\n      if (!application?.id) return;\r\n      \r\n      try {\r\n        setLoading(true);\r\n        const response = await getApplication(application.id);\r\n        setDetailedApplication(response.data);\r\n      } catch (error) {\r\n        console.error('Failed to fetch detailed application:', error);\r\n        // Fallback to basic application data\r\n        setDetailedApplication(application);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDetailedApplication();\r\n  }, [application?.id]);\r\n\r\n  const handleSave = () => {\r\n    // TODO: Implement save functionality\r\n    console.log('Saving application:', editData);\r\n    alert('Save functionality will be implemented in the next phase');\r\n    setIsEditing(false);\r\n    if (onUpdate) onUpdate();\r\n  };\r\n\r\n  if (!application) return null;\r\n\r\n  // Use detailed application data if available, otherwise fallback to basic data\r\n  const appData = detailedApplication || application;\r\n\r\n  const formatDate = (dateString) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const renderAdditionalField = (field, value) => {\r\n    const displayValue = () => {\r\n      if (!value && value !== 0) return 'Not provided';\r\n      \r\n      switch (field.type) {\r\n        case 'file':\r\n          if (typeof value === 'string' && value.startsWith('/media/')) {\r\n            return (\r\n              <a \r\n                href={value} \r\n                target=\"_blank\" \r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-blue-600 hover:text-blue-800 flex items-center gap-1\"\r\n              >\r\n                <Download className=\"w-4 h-4\" />\r\n                View File\r\n              </a>\r\n            );\r\n          }\r\n          return value || 'No file uploaded';\r\n        case 'multiple_choice':\r\n          return value || 'Not selected';\r\n        case 'number':\r\n          return value || 'Not provided';\r\n        case 'text':\r\n        default:\r\n          return value || 'Not provided';\r\n      }\r\n    };\r\n\r\n    return (\r\n      <div key={field.id || field.label} className=\"flex justify-between items-start text-sm py-2 border-b border-gray-100 last:border-b-0\">\r\n        <div className=\"flex-1\">\r\n          <span className=\"font-medium text-gray-700\">\r\n            {field.label}\r\n            {field.required && <span className=\"text-red-500 ml-1\">*</span>}\r\n          </span>\r\n          {field.type && (\r\n            <span className=\"text-xs text-gray-500 ml-2\">({field.type})</span>\r\n          )}\r\n        </div>\r\n        <div className=\"flex-1 text-right\">\r\n          {displayValue()}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto\">\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900\">\r\n            Application Details - {appData.student_name}\r\n          </h2>\r\n          <div className=\"flex items-center gap-3\">\r\n            {!isEditing ? (\r\n              <button\r\n                onClick={() => setIsEditing(true)}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\r\n              >\r\n                Edit\r\n              </button>\r\n            ) : (\r\n              <div className=\"flex gap-2\">\r\n                <button\r\n                  onClick={() => setIsEditing(false)}\r\n                  className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleSave}\r\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2\"\r\n                >\r\n                  <Save className=\"w-4 h-4\" />\r\n                  Save\r\n                </button>\r\n              </div>\r\n            )}\r\n            <button\r\n              onClick={onClose}\r\n              className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n            >\r\n              <X className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-6\">\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n                <p className=\"text-gray-600\">Loading application details...</p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n            {/* Student Information */}\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <User className=\"w-5 h-5 text-gray-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Student Information</h3>\r\n                </div>\r\n                <div className=\"space-y-3\">\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Name</label>\r\n                    <p className=\"text-gray-900\">{appData.student_name}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Email</label>\r\n                    <p className=\"text-gray-900\">{appData.student_email}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Student ID</label>\r\n                    <p className=\"text-gray-900\">{appData.student_id}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Branch</label>\r\n                    <p className=\"text-gray-900\">{appData.branch}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Job Information */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <Briefcase className=\"w-5 h-5 text-gray-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Job Information</h3>\r\n                </div>\r\n                <div className=\"space-y-3\">\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Job Title</label>\r\n                    <p className=\"text-gray-900\">{appData.job_title}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Company</label>\r\n                    <p className=\"text-gray-900\">{appData.company_name}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Location</label>\r\n                    <p className=\"text-gray-900\">{appData.job_location}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Application Details */}\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <Calendar className=\"w-5 h-5 text-gray-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Application Details</h3>\r\n                </div>\r\n                <div className=\"space-y-3\">\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Applied Date</label>\r\n                    <p className=\"text-gray-900\">{formatDate(appData.applied_at)}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-600\">Status</label>\r\n                    {isEditing ? (\r\n                      <select\r\n                        value={editData.status}\r\n                        onChange={(e) => setEditData({ ...editData, status: e.target.value })}\r\n                        className=\"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                      >\r\n                        <option value=\"APPLIED\">Applied</option>\r\n                        <option value=\"UNDER_REVIEW\">Under Review</option>\r\n                        <option value=\"SHORTLISTED\">Shortlisted</option>\r\n                        <option value=\"REJECTED\">Rejected</option>\r\n                        <option value=\"HIRED\">Hired</option>\r\n                      </select>\r\n                    ) : (\r\n                      <div className=\"mt-1\">\r\n                        <StatusBadge status={appData.status} />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  {appData.cover_letter && (\r\n                    <div>\r\n                      <label className=\"text-sm font-medium text-gray-600\">Cover Letter</label>\r\n                      <p className=\"text-gray-900 text-sm bg-white p-3 rounded border\">\r\n                        {appData.cover_letter}\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Fields */}\r\n              {appData.job_additional_fields && appData.job_additional_fields.length > 0 && appData.custom_responses && (\r\n                <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                  <div className=\"flex items-center gap-3 mb-4\">\r\n                    <FileCheck className=\"w-5 h-5 text-gray-600\" />\r\n                    <h3 className=\"text-lg font-semibold text-gray-900\">Additional Information</h3>\r\n                  </div>\r\n                  <div className=\"space-y-1\">\r\n                    {appData.job_additional_fields.map((field) => {\r\n                      const fieldKey = `field_${field.id}`;\r\n                      const value = appData.custom_responses[fieldKey] || appData.custom_responses[field.id] || appData.custom_responses[field.label];\r\n                      return renderAdditionalField(field, value);\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Admin Notes */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <FileText className=\"w-5 h-5 text-gray-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Admin Notes</h3>\r\n                </div>\r\n                {isEditing ? (\r\n                  <textarea\r\n                    value={editData.admin_notes}\r\n                    onChange={(e) => setEditData({ ...editData, admin_notes: e.target.value })}\r\n                    placeholder=\"Add internal notes about this application...\"\r\n                    rows={4}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                ) : (\r\n                  <p className=\"text-gray-900\">\r\n                    {appData.admin_notes || 'No admin notes yet.'}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Status History */}\r\n              {appData.status_history && appData.status_history.length > 0 && (\r\n                <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Status History</h3>\r\n                  <div className=\"space-y-2\">\r\n                    {appData.status_history.map((change, index) => (\r\n                      <div key={index} className=\"text-sm bg-white p-2 rounded\">\r\n                        <span className=\"font-medium\">{change.from_status}</span> → <span className=\"font-medium\">{change.to_status}</span>\r\n                        <span className=\"text-gray-500 ml-2\">{formatDate(change.changed_at)}</span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAOe,SAAS,uBAAuB,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE;;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ,aAAa,UAAU;QAC/B,aAAa,aAAa,eAAe;IAC3C;IACA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;6EAA2B;oBAC/B,IAAI,CAAC,aAAa,IAAI;oBAEtB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,EAAE;wBACpD,uBAAuB,SAAS,IAAI;oBACtC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yCAAyC;wBACvD,qCAAqC;wBACrC,uBAAuB;oBACzB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;2CAAG;QAAC,aAAa;KAAG;IAEpB,MAAM,aAAa;QACjB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,MAAM;QACN,aAAa;QACb,IAAI,UAAU;IAChB;IAEA,IAAI,CAAC,aAAa,OAAO;IAEzB,+EAA+E;IAC/E,MAAM,UAAU,uBAAuB;IAEvC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,wBAAwB,CAAC,OAAO;QACpC,MAAM,eAAe;YACnB,IAAI,CAAC,SAAS,UAAU,GAAG,OAAO;YAElC,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,IAAI,OAAO,UAAU,YAAY,MAAM,UAAU,CAAC,YAAY;wBAC5D,qBACE,6LAAC;4BACC,MAAM;4BACN,QAAO;4BACP,KAAI;4BACJ,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;oBAItC;oBACA,OAAO,SAAS;gBAClB,KAAK;oBACH,OAAO,SAAS;gBAClB,KAAK;oBACH,OAAO,SAAS;gBAClB,KAAK;gBACL;oBACE,OAAO,SAAS;YACpB;QACF;QAEA,qBACE,6LAAC;YAAkC,WAAU;;8BAC3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCACb,MAAM,KAAK;gCACX,MAAM,QAAQ,kBAAI,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;wBAExD,MAAM,IAAI,kBACT,6LAAC;4BAAK,WAAU;;gCAA6B;gCAAE,MAAM,IAAI;gCAAC;;;;;;;;;;;;;8BAG9D,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;WAXK,MAAM,EAAE,IAAI,MAAM,KAAK;;;;;IAerC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAsC;gCAC3B,QAAQ,YAAY;;;;;;;sCAE7C,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,0BACA,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;yDAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAKlC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;6CAIjC,6LAAC;wBAAI,WAAU;;0CAEf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,YAAY;;;;;;;;;;;;kEAEpD,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,aAAa;;;;;;;;;;;;kEAErD,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,UAAU;;;;;;;;;;;;kEAElD,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAMlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,SAAS;;;;;;;;;;;;kEAEjD,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,YAAY;;;;;;;;;;;;kEAEpD,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EAAiB,WAAW,QAAQ,UAAU;;;;;;;;;;;;kEAE7D,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;4DACpD,0BACC,6LAAC;gEACC,OAAO,SAAS,MAAM;gEACtB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,6LAAC;wEAAO,OAAM;kFAAe;;;;;;kFAC7B,6LAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,6LAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;qFAGxB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,oJAAA,CAAA,UAAW;oEAAC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;oDAIxC,QAAQ,YAAY,kBACnB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAE,WAAU;0EACV,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,QAAQ,qBAAqB,IAAI,QAAQ,qBAAqB,CAAC,MAAM,GAAG,KAAK,QAAQ,gBAAgB,kBACpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,qBAAqB,CAAC,GAAG,CAAC,CAAC;oDAClC,MAAM,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;oDACpC,MAAM,QAAQ,QAAQ,gBAAgB,CAAC,SAAS,IAAI,QAAQ,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,QAAQ,gBAAgB,CAAC,MAAM,KAAK,CAAC;oDAC/H,OAAO,sBAAsB,OAAO;gDACtC;;;;;;;;;;;;kDAMN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;4CAErD,0BACC,6LAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,aAAY;gDACZ,MAAM;gDACN,WAAU;;;;;qEAGZ,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW,IAAI;;;;;;;;;;;;oCAM7B,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,mBACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACnC,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAK,WAAU;0EAAe,OAAO,WAAW;;;;;;4DAAQ;0EAAG,6LAAC;gEAAK,WAAU;0EAAe,OAAO,SAAS;;;;;;0EAC3G,6LAAC;gEAAK,WAAU;0EAAsB,WAAW,OAAO,UAAU;;;;;;;uDAF1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC;GAhTwB;KAAA", "debugId": null}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/applications/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState, Suspense } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport {\r\n  Search,\r\n  Filter,\r\n  Download,\r\n  Eye,\r\n  Edit,\r\n  Trash2,\r\n  CheckCircle,\r\n  Clock,\r\n  XCircle,\r\n  Calendar,\r\n  Building2,\r\n  User,\r\n  FileText,\r\n  MoreHorizontal,\r\n  RefreshCw\r\n} from 'lucide-react';\r\nimport { getAllApplications, getApplicationStats, deleteApplication } from '../../../api/applications';\r\nimport ApplicationFilters from './components/ApplicationFilters';\r\nimport ApplicationsTable from './components/ApplicationsTable';\r\nimport ExportModal from './components/ExportModal';\r\nimport ApplicationDetailModal from './components/ApplicationDetailModal';\r\nimport StatusBadge from '../../../components/applications/StatusBadge';\r\n\r\nfunction ApplicationsPageContent() {\r\n  const searchParams = useSearchParams();\r\n  const jobIdFromUrl = searchParams.get('job_id');\r\n\r\n  // State management\r\n  const [applications, setApplications] = useState([]);\r\n  const [filteredApplications, setFilteredApplications] = useState([]);\r\n  const [stats, setStats] = useState({\r\n    total: 0,\r\n    by_status: [],\r\n    recent: 0\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // UI state\r\n  const [selectedApplication, setSelectedApplication] = useState(null);\r\n  const [showExportModal, setShowExportModal] = useState(false);\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  const [showDetailModal, setShowDetailModal] = useState(false);\r\n\r\n  // Filter and search state\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filters, setFilters] = useState({\r\n    status: 'ALL',\r\n    company: '',\r\n    job_title: '',\r\n    date_from: '',\r\n    date_to: '',\r\n    student_name: ''\r\n  });\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [itemsPerPage] = useState(20);\r\n\r\n  // Load applications data\r\n  useEffect(() => {\r\n    loadApplications();\r\n  }, [currentPage, filters, jobIdFromUrl]);\r\n\r\n  // Initialize filtered applications when applications data changes\r\n  useEffect(() => {\r\n    if (!searchTerm.trim()) {\r\n      setFilteredApplications(applications);\r\n    }\r\n  }, [applications]);\r\n\r\n  // Search debouncing\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      applySearchFilter();\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchTerm, applications]);\r\n\r\n  const loadApplications = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Try to fetch real data first since we now have real applications\r\n      try {\r\n        const response = await getAllApplications({\r\n          page: currentPage,\r\n          page_size: itemsPerPage,\r\n          ...(jobIdFromUrl && { job_id: jobIdFromUrl }),\r\n          ...filters\r\n        });\r\n\r\n        console.log('Real API Response:', response);\r\n\r\n        if (response && response.data) {\r\n          // Backend returns paginated response with nested structure\r\n          // Structure: { count, next, previous, results: { results: [...], stats: {...} } }\r\n\r\n          let applications = [];\r\n          let stats = { total: 0, by_status: [], recent: 0 };\r\n          let totalCount = 0;\r\n\r\n          if (response.data.results) {\r\n            if (typeof response.data.results === 'object' && response.data.results.results) {\r\n              // Nested structure: results: { results: [...], stats: {...} }\r\n              applications = response.data.results.results || [];\r\n              stats = response.data.results.stats || stats;\r\n              totalCount = response.data.count || stats.total || 0;\r\n            } else if (Array.isArray(response.data.results)) {\r\n              // Simple array structure: results: [...]\r\n              applications = response.data.results;\r\n              stats = response.data.stats || stats;\r\n              totalCount = response.data.count || applications.length;\r\n            }\r\n          }\r\n\r\n          console.log('Processed applications:', applications.length);\r\n          console.log('Processed stats:', stats);\r\n\r\n          setApplications(applications);\r\n          setStats(stats);\r\n          setTotalPages(Math.ceil(totalCount / itemsPerPage));\r\n\r\n          return; // Successfully loaded real data\r\n        }\r\n      } catch (apiError) {\r\n        console.error('API Error details:', {\r\n          message: apiError.message,\r\n          status: apiError.response?.status,\r\n          statusText: apiError.response?.statusText,\r\n          data: apiError.response?.data,\r\n          url: apiError.config?.url\r\n        });\r\n        console.log('API Error, falling back to mock data:', apiError);\r\n      }\r\n\r\n      // Fallback to mock data if API fails\r\n      let mockApplications = [\r\n        {\r\n          id: 1,\r\n          job_id: 25,\r\n          student_name: \"John Doe\",\r\n          student_email: \"<EMAIL>\",\r\n          student_id: \"CS2021001\",\r\n          branch: \"Computer Science\",\r\n          job_title: \"Software Engineer\",\r\n          company_name: \"TechCorp Inc\",\r\n          job_location: \"San Francisco, CA\",\r\n          status: \"APPLIED\",\r\n          applied_at: \"2024-01-15T10:30:00Z\",\r\n          cover_letter: \"I am very interested in this position...\",\r\n          admin_notes: \"\",\r\n          status_history: []\r\n        },\r\n        {\r\n          id: 2,\r\n          job_id: 25,\r\n          student_name: \"Jane Smith\",\r\n          student_email: \"<EMAIL>\",\r\n          student_id: \"CS2021002\",\r\n          branch: \"Computer Science\",\r\n          job_title: \"Software Engineer\",\r\n          company_name: \"TechCorp Inc\",\r\n          job_location: \"San Francisco, CA\",\r\n          status: \"UNDER_REVIEW\",\r\n          applied_at: \"2024-01-14T14:20:00Z\",\r\n          cover_letter: \"My experience in machine learning...\",\r\n          admin_notes: \"Strong candidate\",\r\n          status_history: []\r\n        },\r\n        {\r\n          id: 3,\r\n          job_id: 26,\r\n          student_name: \"Mike Johnson\",\r\n          student_email: \"<EMAIL>\",\r\n          student_id: \"CS2021003\",\r\n          branch: \"Computer Science\",\r\n          job_title: \"Frontend Developer\",\r\n          company_name: \"WebSolutions\",\r\n          job_location: \"Austin, TX\",\r\n          status: \"SHORTLISTED\",\r\n          applied_at: \"2024-01-13T09:15:00Z\",\r\n          cover_letter: \"I have extensive experience in React...\",\r\n          admin_notes: \"Excellent portfolio\",\r\n          status_history: []\r\n        }\r\n      ];\r\n\r\n      // Filter by job_id if provided in URL\r\n      if (jobIdFromUrl) {\r\n        mockApplications = mockApplications.filter(app => app.job_id === parseInt(jobIdFromUrl));\r\n      }\r\n\r\n      // Calculate stats AFTER filtering\r\n      const mockStats = {\r\n        total: mockApplications.length,\r\n        by_status: [\r\n          { status: 'APPLIED', count: mockApplications.filter(app => app.status === 'APPLIED').length },\r\n          { status: 'UNDER_REVIEW', count: mockApplications.filter(app => app.status === 'UNDER_REVIEW').length },\r\n          { status: 'SHORTLISTED', count: mockApplications.filter(app => app.status === 'SHORTLISTED').length }\r\n        ],\r\n        recent: mockApplications.length // All mock data is recent for demo\r\n      };\r\n\r\n      console.log('Using Mock Applications:', mockApplications);\r\n      console.log('Mock Stats:', mockStats);\r\n      console.log('Job ID from URL:', jobIdFromUrl);\r\n\r\n      setApplications(mockApplications);\r\n      setStats(mockStats);\r\n      setTotalPages(1);\r\n\r\n    } catch (err) {\r\n      console.error('Failed to load applications:', err);\r\n      setError('Failed to load applications');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const applySearchFilter = () => {\r\n    const safeApplications = Array.isArray(applications) ? applications : [];\r\n    \r\n    if (!searchTerm.trim()) {\r\n      setFilteredApplications(safeApplications);\r\n      return;\r\n    }\r\n\r\n    const filtered = safeApplications.filter(app => \r\n      app.student_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      app.student_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      app.job_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      app.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      app.student_id?.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    setFilteredApplications(filtered);\r\n  };\r\n\r\n  const handleFilterChange = (newFilters) => {\r\n    setFilters(newFilters);\r\n    setCurrentPage(1); // Reset to first page when filters change\r\n  };\r\n\r\n  const handleViewApplication = (application) => {\r\n    setSelectedApplication(application);\r\n    setShowDetailModal(true);\r\n  };\r\n\r\n  const handleDeleteApplication = async (applicationId) => {\r\n    if (!confirm('Are you sure you want to delete this application? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // For now, just remove from local state since API might not be ready\r\n      setApplications(prev => prev.filter(app => app.id !== applicationId));\r\n      alert('Application deleted successfully (mock action)');\r\n    } catch (err) {\r\n      console.error('Failed to delete application:', err);\r\n      alert('Failed to delete application');\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status) => {\r\n    const iconMap = {\r\n      'APPLIED': <Clock className=\"w-4 h-4\" />,\r\n      'UNDER_REVIEW': <Eye className=\"w-4 h-4\" />,\r\n      'SHORTLISTED': <CheckCircle className=\"w-4 h-4\" />,\r\n      'REJECTED': <XCircle className=\"w-4 h-4\" />,\r\n      'HIRED': <CheckCircle className=\"w-4 h-4\" />\r\n    };\r\n    return iconMap[status] || <Clock className=\"w-4 h-4\" />;\r\n  };\r\n\r\n  const displayApplications = searchTerm \r\n    ? (Array.isArray(filteredApplications) ? filteredApplications : []) \r\n    : (Array.isArray(applications) ? applications : []);\r\n\r\n  // Debug logging\r\n  console.log('Current state:', {\r\n    searchTerm,\r\n    applications: applications?.length || 0,\r\n    filteredApplications: filteredApplications?.length || 0,\r\n    displayApplications: displayApplications?.length || 0,\r\n    stats\r\n  });\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-gray-900\">Applications Management</h1>\r\n          <p className=\"text-gray-600 mt-1\">\r\n            {jobIdFromUrl \r\n              ? `Applications for Job ID: ${jobIdFromUrl}` \r\n              : 'Track and manage all job applications from students'\r\n            }\r\n          </p>\r\n          {jobIdFromUrl && (\r\n            <div className=\"mt-2\">\r\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n                Filtered by Job ID: {jobIdFromUrl}\r\n              </span>\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <button\r\n            onClick={() => setShowFilters(!showFilters)}\r\n            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${\r\n              showFilters \r\n                ? 'bg-blue-50 border-blue-200 text-blue-700' \r\n                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\r\n            }`}\r\n          >\r\n            <Filter className=\"w-4 h-4\" />\r\n            Filters\r\n          </button>\r\n          \r\n          <button\r\n            onClick={() => setShowExportModal(true)}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\r\n          >\r\n            <Download className=\"w-4 h-4\" />\r\n            Export\r\n          </button>\r\n          \r\n          <button\r\n            onClick={loadApplications}\r\n            disabled={loading}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50\"\r\n          >\r\n            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />\r\n            Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Statistics Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">Total Applications</p>\r\n              <p className=\"text-3xl font-bold text-gray-900\">{stats.total}</p>\r\n            </div>\r\n            <div className=\"p-3 bg-blue-100 rounded-full\">\r\n              <FileText className=\"w-6 h-6 text-blue-600\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">Pending Review</p>\r\n              <p className=\"text-3xl font-bold text-gray-900\">\r\n                {stats.by_status?.find(s => s.status === 'APPLIED')?.count || 0}\r\n              </p>\r\n            </div>\r\n            <div className=\"p-3 bg-yellow-100 rounded-full\">\r\n              <Clock className=\"w-6 h-6 text-yellow-600\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">Shortlisted</p>\r\n              <p className=\"text-3xl font-bold text-gray-900\">\r\n                {stats.by_status?.find(s => s.status === 'SHORTLISTED')?.count || 0}\r\n              </p>\r\n            </div>\r\n            <div className=\"p-3 bg-green-100 rounded-full\">\r\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">Recent (7 days)</p>\r\n              <p className=\"text-3xl font-bold text-gray-900\">{stats.recent}</p>\r\n            </div>\r\n            <div className=\"p-3 bg-purple-100 rounded-full\">\r\n              <Calendar className=\"w-6 h-6 text-purple-600\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters Panel */}\r\n      {showFilters && (\r\n        <ApplicationFilters\r\n          filters={filters}\r\n          onFilterChange={handleFilterChange}\r\n          onClose={() => setShowFilters(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Search Bar */}\r\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search by student name, email, job title, company, or student ID...\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Applications Table */}\r\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\r\n        {loading ? (\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <RefreshCw className=\"w-8 h-8 text-gray-400 animate-spin\" />\r\n            <span className=\"ml-3 text-gray-600\">Loading applications...</span>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"text-center\">\r\n              <XCircle className=\"w-12 h-12 text-red-400 mx-auto mb-4\" />\r\n              <p className=\"text-gray-900 font-medium\">Error loading applications</p>\r\n              <p className=\"text-gray-600\">{error}</p>\r\n              <button\r\n                onClick={loadApplications}\r\n                className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : displayApplications.length === 0 ? (\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"text-center\">\r\n              <FileText className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n              <p className=\"text-gray-900 font-medium\">No applications found</p>\r\n              <p className=\"text-gray-600\">\r\n                {searchTerm ? 'Try adjusting your search criteria' : 'No applications have been submitted yet'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <ApplicationsTable\r\n            applications={displayApplications}\r\n            onViewApplication={handleViewApplication}\r\n            onDeleteApplication={handleDeleteApplication}\r\n          />\r\n        )}\r\n\r\n        {/* Pagination */}\r\n        {totalPages > 1 && (\r\n          <div className=\"flex items-center justify-between px-6 py-4 border-t border-gray-200\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-sm text-gray-600\">\r\n                Page {currentPage} of {totalPages}\r\n              </span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-2\">\r\n              <button\r\n                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\r\n                disabled={currentPage === 1}\r\n                className=\"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Previous\r\n              </button>\r\n              \r\n              <button\r\n                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\r\n                disabled={currentPage === totalPages}\r\n                className=\"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Next\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      {showExportModal && (\r\n        <ExportModal\r\n          onClose={() => setShowExportModal(false)}\r\n          filters={filters}\r\n        />\r\n      )}\r\n\r\n      {showDetailModal && selectedApplication && (\r\n        <ApplicationDetailModal\r\n          application={selectedApplication}\r\n          onClose={() => {\r\n            setShowDetailModal(false);\r\n            setSelectedApplication(null);\r\n          }}\r\n          onUpdate={loadApplications}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function ApplicationsPage() {\r\n  return (\r\n    <Suspense fallback={<div>Loading...</div>}>\r\n      <ApplicationsPageContent />\r\n    </Suspense>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;;;AA1BA;;;;;;;;;;AA4BA,SAAS;;IACP,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,mBAAmB;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,OAAO;QACP,WAAW,EAAE;QACb,QAAQ;IACV;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,WAAW;IACX,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,0BAA0B;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,cAAc;IAChB;IAEA,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEhC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;QACF;4CAAG;QAAC;QAAa;QAAS;KAAa;IAEvC,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,wBAAwB;YAC1B;QACF;4CAAG;QAAC;KAAa;IAEjB,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,YAAY;+DAAW;oBAC3B;gBACF;8DAAG;YAEH;qDAAO,IAAM,aAAa;;QAC5B;4CAAG;QAAC;QAAY;KAAa;IAE7B,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YAEX,mEAAmE;YACnE,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;oBACxC,MAAM;oBACN,WAAW;oBACX,GAAI,gBAAgB;wBAAE,QAAQ;oBAAa,CAAC;oBAC5C,GAAG,OAAO;gBACZ;gBAEA,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,IAAI,YAAY,SAAS,IAAI,EAAE;oBAC7B,2DAA2D;oBAC3D,kFAAkF;oBAElF,IAAI,eAAe,EAAE;oBACrB,IAAI,QAAQ;wBAAE,OAAO;wBAAG,WAAW,EAAE;wBAAE,QAAQ;oBAAE;oBACjD,IAAI,aAAa;oBAEjB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;wBACzB,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO,KAAK,YAAY,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;4BAC9E,8DAA8D;4BAC9D,eAAe,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;4BAClD,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI;4BACvC,aAAa,SAAS,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,IAAI;wBACrD,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,GAAG;4BAC/C,yCAAyC;4BACzC,eAAe,SAAS,IAAI,CAAC,OAAO;4BACpC,QAAQ,SAAS,IAAI,CAAC,KAAK,IAAI;4BAC/B,aAAa,SAAS,IAAI,CAAC,KAAK,IAAI,aAAa,MAAM;wBACzD;oBACF;oBAEA,QAAQ,GAAG,CAAC,2BAA2B,aAAa,MAAM;oBAC1D,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,gBAAgB;oBAChB,SAAS;oBACT,cAAc,KAAK,IAAI,CAAC,aAAa;oBAErC,QAAQ,gCAAgC;gBAC1C;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,sBAAsB;oBAClC,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,QAAQ,EAAE;oBAC3B,YAAY,SAAS,QAAQ,EAAE;oBAC/B,MAAM,SAAS,QAAQ,EAAE;oBACzB,KAAK,SAAS,MAAM,EAAE;gBACxB;gBACA,QAAQ,GAAG,CAAC,yCAAyC;YACvD;YAEA,qCAAqC;YACrC,IAAI,mBAAmB;gBACrB;oBACE,IAAI;oBACJ,QAAQ;oBACR,cAAc;oBACd,eAAe;oBACf,YAAY;oBACZ,QAAQ;oBACR,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,QAAQ;oBACR,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,gBAAgB,EAAE;gBACpB;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,cAAc;oBACd,eAAe;oBACf,YAAY;oBACZ,QAAQ;oBACR,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,QAAQ;oBACR,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,gBAAgB,EAAE;gBACpB;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,cAAc;oBACd,eAAe;oBACf,YAAY;oBACZ,QAAQ;oBACR,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,QAAQ;oBACR,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,gBAAgB,EAAE;gBACpB;aACD;YAED,sCAAsC;YACtC,IAAI,cAAc;gBAChB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,SAAS;YAC5E;YAEA,kCAAkC;YAClC,MAAM,YAAY;gBAChB,OAAO,iBAAiB,MAAM;gBAC9B,WAAW;oBACT;wBAAE,QAAQ;wBAAW,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;oBAAC;oBAC5F;wBAAE,QAAQ;wBAAgB,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,gBAAgB,MAAM;oBAAC;oBACtG;wBAAE,QAAQ;wBAAe,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,eAAe,MAAM;oBAAC;iBACrG;gBACD,QAAQ,iBAAiB,MAAM,CAAC,mCAAmC;YACrE;YAEA,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,gBAAgB;YAChB,SAAS;YACT,cAAc;QAEhB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,mBAAmB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;QAExE,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,wBAAwB;YACxB;QACF;QAEA,MAAM,WAAW,iBAAiB,MAAM,CAAC,CAAA,MACvC,IAAI,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,IAAI,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,IAAI,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC5D,IAAI,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,IAAI,UAAU,EAAE,cAAc,SAAS,WAAW,WAAW;QAG/D,wBAAwB;IAC1B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW;QACX,eAAe,IAAI,0CAA0C;IAC/D;IAEA,MAAM,wBAAwB,CAAC;QAC7B,uBAAuB;QACvB,mBAAmB;IACrB;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,QAAQ,oFAAoF;YAC/F;QACF;QAEA,IAAI;YACF,qEAAqE;YACrE,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACtD,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU;YACd,yBAAW,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAC5B,8BAAgB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAC/B,6BAAe,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YACtC,0BAAY,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAC/B,uBAAS,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAClC;QACA,OAAO,OAAO,CAAC,OAAO,kBAAI,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IAC7C;IAEA,MAAM,sBAAsB,aACvB,MAAM,OAAO,CAAC,wBAAwB,uBAAuB,EAAE,GAC/D,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;IAEpD,gBAAgB;IAChB,QAAQ,GAAG,CAAC,kBAAkB;QAC5B;QACA,cAAc,cAAc,UAAU;QACtC,sBAAsB,sBAAsB,UAAU;QACtD,qBAAqB,qBAAqB,UAAU;QACpD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CACV,eACG,CAAC,yBAAyB,EAAE,cAAc,GAC1C;;;;;;4BAGL,8BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAoG;wCAC7F;;;;;;;;;;;;;;;;;;kCAM7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAW,CAAC,sEAAsE,EAChF,cACI,6CACA,2DACJ;;kDAEF,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIhC,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIlC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;;;;;;;;;;;;;0BAO1E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,KAAK;;;;;;;;;;;;8CAE9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,MAAM,SAAS,EAAE,KAAK,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,SAAS;;;;;;;;;;;;8CAGlE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACV,MAAM,SAAS,EAAE,KAAK,CAAA,IAAK,EAAE,MAAM,KAAK,gBAAgB,SAAS;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,MAAM;;;;;;;;;;;;8CAE/D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3B,6BACC,6LAAC,2KAAA,CAAA,UAAkB;gBACjB,SAAS;gBACT,gBAAgB;gBAChB,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;oBACZ,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;+BAErC,sBACF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAiB;;;;;;8CAC9B,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;+BAKH,oBAAoB,MAAM,KAAK,kBACjC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CACV,aAAa,uCAAuC;;;;;;;;;;;;;;;;6CAK3D,6LAAC,0KAAA,CAAA,UAAiB;wBAChB,cAAc;wBACd,mBAAmB;wBACnB,qBAAqB;;;;;;oBAKxB,aAAa,mBACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAwB;wCAChC;wCAAY;wCAAK;;;;;;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wCACxD,UAAU,gBAAgB;wCAC1B,WAAU;kDACX;;;;;;kDAID,6LAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wCACjE,UAAU,gBAAgB;wCAC1B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YASR,iCACC,6LAAC,oKAAA,CAAA,UAAW;gBACV,SAAS,IAAM,mBAAmB;gBAClC,SAAS;;;;;;YAIZ,mBAAmB,qCAClB,6LAAC,+KAAA,CAAA,UAAsB;gBACrB,aAAa;gBACb,SAAS;oBACP,mBAAmB;oBACnB,uBAAuB;gBACzB;gBACA,UAAU;;;;;;;;;;;;AAKpB;GAveS;;QACc,qIAAA,CAAA,kBAAe;;;KAD7B;AAyeM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;sBAAI;;;;;;kBACvB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}